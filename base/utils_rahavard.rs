/*!
<PERSON><PERSON>vard utility functions.

This module provides Rust equivalents of the Python utility functions
for date/time handling, string manipulation, file operations, and various
helper functions used throughout the Eterna project.
*/

use std::collections::HashMap;
use std::fs;
use std::path::Path;
use regex::Regex;
use chrono::{DateTime, Utc, NaiveDateTime};
use uuid::Uuid;
use once_cell::sync::Lazy;
use natural_sort::natural_sort;

/// Admin configuration constants
pub const ADMIN_LIST_DISPLAY_LINKS: &[&str] = &["id", "short_uuid"];
pub const ADMIN_READONLY_FIELDS: &[&str] = &["id", "short_uuid", "created", "updated"];
pub const ADMIN_LIST_FILTER: &[&str] = &["active", "short_uuid"];
pub const ADMIN_USER_READONLY_FIELDS: &[&str] = &["id", "short_uuid", "date_joined", "last_login"];
pub const ADMIN_USER_LIST_FILTER: &[&str] = &["is_superuser", "is_staff", "is_active", "is_limited_admin", "short_uuid"];

/// Jalali date format string
pub const JALALI_FORMAT: &str = "%A %H %M %S %d %m %Y";

/// Regular expressions for date and time patterns
pub static YMD_REGEX: Lazy<Regex> = Lazy::new(|| Regex::new(r"[0-9]{4}-[0-9]{2}-[0-9]{2}").unwrap());
pub static HMS_REGEX: Lazy<Regex> = Lazy::new(|| Regex::new(r"[0-9]{2}:[0-9]{2}:[0-9]{2}").unwrap());
pub static INT_OR_FLOAT_PATTERN: Lazy<Regex> = Lazy::new(|| Regex::new(r"^[0-9\.]+$").unwrap());

/// Size suffixes for byte conversion
pub static SIZE_SUFFIXES: Lazy<HashMap<&'static str, Vec<&'static str>>> = Lazy::new(|| {
    let mut map = HashMap::new();
    map.insert("persian", vec![
        "بایت",
        "کیلوبایت",
        "مگابایت",
        "گیگابایت",
        "ترابایت",
        "پتابایت",
        "اگزابایت",
        "زتابایت",
        "یوتابایت",
    ]);
    map.insert("latin", vec![
        "B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB",
    ]);
    map
});


/// Check if a string contains a date in the format YYYY-MM-DD.
///
/// This function uses a regular expression to determine if the input string contains a date in the format YYYY-MM-DD.
///
/// # Arguments
/// * `string` - The input string to be checked.
///
/// # Returns
/// `true` if the string contains a date in the format YYYY-MM-DD, `false` otherwise.
///
/// # Examples
/// ```
/// assert_eq!(contains_ymd("Today's date is 2023-10-05."), true);
/// assert_eq!(contains_ymd("No date here!"), false);
/// assert_eq!(contains_ymd("The event is on 2023-12-25."), true);
/// assert_eq!(contains_ymd("Date: 2023/10/05"), false);
/// ```
pub fn contains_ymd(string: &str) -> bool {
    YMD_REGEX.is_match(string)
}

/// Check if a given string matches the Year-Month-Day (YMD) format.
///
/// # Arguments
/// * `string` - The string to be checked.
///
/// # Returns
/// `true` if the string matches the YMD format, `false` otherwise.
///
/// # Examples
/// ```
/// assert_eq!(is_ymd("2023-10-05"), true);
/// assert_eq!(is_ymd("05-10-2023"), false);
/// assert_eq!(is_ymd("2023/10/05"), false);
/// assert_eq!(is_ymd("20231005"), false);
/// ```
pub fn is_ymd(string: &str) -> bool {
    let full_pattern = format!("^{}$", YMD_REGEX.as_str());
    let regex = Regex::new(&full_pattern).unwrap();
    regex.is_match(string)
}

/// Check if a string starts with a date and time in the format 'YYYY-MM-DD HH:MM:SS'.
///
/// # Arguments
/// * `string` - The string to be checked.
///
/// # Returns
/// `true` if the string starts with a date and time in the specified format, `false` otherwise.
///
/// # Examples
/// ```
/// assert_eq!(starts_with_ymdhms("2023-10-05 12:34:56 Some event"), true);
/// assert_eq!(starts_with_ymdhms("Some event 2023-10-05 12:34:56"), false);
/// assert_eq!(starts_with_ymdhms("2023-10-05 12:34:56"), false);
/// assert_eq!(starts_with_ymdhms("2023-10-05 12:34 Some event"), false);
/// ```
pub fn starts_with_ymdhms(string: &str) -> bool {
    let pattern = format!("^{} {} ", YMD_REGEX.as_str(), HMS_REGEX.as_str());
    let regex = Regex::new(&pattern).unwrap();
    regex.is_match(string)
}

// ---------------------------------

/// Calculate the offset for a given page number and limit to show in a MySQL query.
///
/// # Arguments
/// * `page_number` - The current page number (1-based index).
/// * `limit_to_show` - The number of items to show per page (MySQL LIMIT).
///
/// # Returns
/// The offset to be used in a MySQL query.
///
/// # Examples
/// ```
/// assert_eq!(calculate_offset(1, 25), 0);
/// assert_eq!(calculate_offset(2, 25), 25);
/// assert_eq!(calculate_offset(3, 10), 20);
/// ```
pub fn calculate_offset(page_number: i32, limit_to_show: i32) -> i32 {
    (page_number - 1) * limit_to_show
}

/// Check if the request comes from HTMX.
///
/// This function inspects the headers of a request to determine if it
/// originated from an HTMX request by checking for the presence of the 'HX-Request' header.
///
/// # Arguments
/// * `headers` - A HashMap containing the request headers.
///
/// # Returns
/// `true` if the request contains the 'HX-Request' header, `false` otherwise.
///
/// # Examples
/// ```
/// use std::collections::HashMap;
/// let mut headers = HashMap::new();
/// headers.insert("HX-Request".to_string(), "true".to_string());
/// assert_eq!(comes_from_htmx(&headers), true);
///
/// let empty_headers = HashMap::new();
/// assert_eq!(comes_from_htmx(&empty_headers), false);
/// ```
pub fn comes_from_htmx(headers: &HashMap<String, String>) -> bool {
    headers.contains_key("HX-Request")
}

/// Convert a size in bytes to a human-readable string format.
///
/// # Arguments
/// * `size_in_bytes` - The size in bytes to be converted.
/// * `to_persian` - If true, the output will be in Persian. Default is false.
///
/// # Returns
/// The human-readable string representation of the size.
///
/// # Examples
/// ```
/// assert_eq!(convert_byte(1024.0, false), "1KB");
/// assert_eq!(convert_byte(1048576.0, false), "1MB");
/// assert_eq!(convert_byte(0.0, false), "0B");
/// assert_eq!(convert_byte(1024.0, true), "۱ کیلوبایت");
/// assert_eq!(convert_byte(1048576.0, true), "۱ مگابایت");
/// ```
pub fn convert_byte(size_in_bytes: f64, to_persian: bool) -> String {
    if !is_int_or_float(&size_in_bytes.to_string()) || size_in_bytes as i64 == 0 {
        return if to_persian {
            "۰ بایت".to_string()
        } else {
            "0B".to_string()
        };
    }

    let i = (size_in_bytes.log2() / 1024_f64.log2()).floor() as usize;
    let p = 1024_f64.powi(i as i32);
    let mut conv = (size_in_bytes / p * 10.0).round() / 10.0;

    // Remove trailing .0
    let conv_str = if conv.fract() == 0.0 {
        format!("{}", conv as i64)
    } else {
        format!("{:.1}", conv)
    };

    let suffixes = if to_persian {
        SIZE_SUFFIXES.get("persian").unwrap()
    } else {
        SIZE_SUFFIXES.get("latin").unwrap()
    };

    if to_persian {
        format!("{} {}", persianize(&conv_str), suffixes[i])
    } else {
        format!("{}{}", conv_str, suffixes[i])
    }
}

/// Convert milliseconds to seconds and return the result.
///
/// # Arguments
/// * `ms` - The time in milliseconds to be converted. If the input is not valid, it defaults to 0.
/// * `verbose` - If true, returns a verbose string representation of the time. If false, returns the time as a float.
///
/// # Returns
/// The converted time in seconds, either as a verbose string or a float, depending on the value of `verbose`.
///
/// # Examples
/// ```
/// assert_eq!(convert_millisecond("1500", true), "1.5 seconds");
/// assert_eq!(convert_millisecond("1500", false), "1.5");
/// assert_eq!(convert_millisecond("invalid", false), "0");
/// assert_eq!(convert_millisecond("0", true), "0");
/// ```
pub fn convert_millisecond(ms: &str, verbose: bool) -> String {
    let ms_value = if is_int_or_float(ms) {
        ms.parse::<f64>().unwrap_or(0.0)
    } else {
        0.0
    };

    convert_second(ms_value / 1000.0, verbose)
}

/// Convert a given number of seconds into a human-readable string format.
///
/// # Arguments
/// * `seconds` - The number of seconds to convert.
/// * `verbose` - If true, returns a verbose string format. If false, returns a compact string format.
///
/// # Returns
/// The converted time in a human-readable string format.
///
/// # Examples
/// ```
/// assert_eq!(convert_second(0.0, true), "0");
/// assert_eq!(convert_second(0.0, false), "0:00");
/// assert_eq!(convert_second(0.56, true), "~0");
/// assert_eq!(convert_second(0.56, false), "~0:00");
/// assert_eq!(convert_second(3661.0, true), "1 hr, 1 min and 1 sec");
/// assert_eq!(convert_second(3661.0, false), "1:01:01");
/// ```
pub fn convert_second(seconds: f64, verbose: bool) -> String {
    if seconds == 0.0 {
        return if verbose { "0".to_string() } else { "0:00".to_string() };
    }

    if seconds < 1.0 {
        return if verbose { "~0".to_string() } else { "~0:00".to_string() };
    }

    let ss = format!("{:02}", (seconds as i64) % 60);
    let mi = format!("{:02}", ((seconds as i64) / 60) % 60);
    let hh = format!("{:02}", ((seconds as i64) / 3600) % 24);
    let dd = format!("{:02}", ((seconds as i64) / 3600 / 24) % 30);
    let mo = format!("{:02}", ((seconds as i64) / 3600 / 24 / 30) % 12);
    let yy = format!("{:02}", (seconds as i64) / 3600 / 24 / 30 / 12);

    let mut result = if yy == "00" && mo == "00" && dd == "00" {
        if verbose {
            format!("{} hrs, {} mins and {} secs", hh, mi, ss)
        } else {
            format!("{}:{}:{}", hh, mi, ss)
        }
    } else if yy == "00" && mo == "00" {
        if verbose {
            format!("{} days, {} hrs and {} mins", dd, hh, mi)
        } else {
            format!("{}:{}:{}:{}", dd, hh, mi, ss)
        }
    } else if yy == "00" {
        if verbose {
            format!("{} months, {} days and {} hrs", mo, dd, hh)
        } else {
            format!("{}:{}:{}:{}:{}", mo, dd, hh, mi, ss)
        }
    } else {
        if verbose {
            format!("{} years, {} months and {} days", yy, mo, dd)
        } else {
            format!("{}:{}:{}:{}:{}:{}", yy, mo, dd, hh, mi, ss)
        }
    };

    if verbose {
        // Clean up the verbose format
        let regex_patterns = vec![
            (r"00 [a-z]+s, ", ""),
            (r"00 [a-z]+s and ", ""),
            (r"00 [a-z]+s$", ""),
            (r", ([0-9][0-9] [a-z]+s )", r" and $1"),
            (r"and 00 [a-z]+s ", ""),
            (r" and $", ""),
            (r", ([0-9][0-9] [a-z]+)$", r" and $1"),
            (r" and ([0-9][0-9] [a-z]+) and", r", $1 and"),
            (r", +$", ""),
            (r", ([0-9][0-9] [a-z]+s)$", r" and $1"),
            (r"(01 [a-z]+)s ", r"$1 "),
            (r"(01 [a-z]+)s, ", r"$1, "),
            (r"(01 [a-z]+)s$", r"$1"),
            (r", 0([0-9])", r", $1"),
            (r"and 0([0-9])", r"and $1"),
        ];

        for (pattern, replacement) in regex_patterns {
            let regex = Regex::new(pattern).unwrap();
            result = regex.replace_all(&result, replacement).to_string();
        }
    } else {
        // Clean up the compact format
        let regex_patterns = vec![
            (r"^0+:0([0-9]):", r"$1:"),
            (r"^0+:([1-9])([0-9]):", r"$1$2:"),
        ];

        for (pattern, replacement) in regex_patterns {
            let regex = Regex::new(pattern).unwrap();
            result = regex.replace_all(&result, replacement).to_string();
        }
    }

    // Final cleanup
    let regex = Regex::new(r"^0([0-9])").unwrap();
    result = regex.replace(&result, "$1").to_string();

    result
}

/// Convert specific string representations to their corresponding values.
///
/// This function converts the strings 'True', 'False', 'None', and '0' to their
/// respective values. If the input string does not match any of these, it returns the input string unchanged.
///
/// # Arguments
/// * `item` - The input string to be converted.
///
/// # Returns
/// An enum representing the converted value or the original string if no conversion is applicable.
///
/// # Examples
/// ```
/// assert_eq!(convert_string_true_false_none_0("True"), ConvertedValue::Bool(true));
/// assert_eq!(convert_string_true_false_none_0("False"), ConvertedValue::Bool(false));
/// assert_eq!(convert_string_true_false_none_0("None"), ConvertedValue::None);
/// assert_eq!(convert_string_true_false_none_0("0"), ConvertedValue::Int(0));
/// assert_eq!(convert_string_true_false_none_0("Hello"), ConvertedValue::String("Hello".to_string()));
/// ```
#[derive(Debug, PartialEq)]
pub enum ConvertedValue {
    Bool(bool),
    None,
    Int(i32),
    String(String),
}

pub fn convert_string_true_false_none_0(item: &str) -> ConvertedValue {
    match item {
        "True" => ConvertedValue::Bool(true),
        "False" => ConvertedValue::Bool(false),
        "None" => ConvertedValue::None,
        "0" => ConvertedValue::Int(0),
        _ => ConvertedValue::String(item.to_string()),
    }
}

/// Convert a Unix timestamp to a Jalali date string.
///
/// This function converts a given Unix timestamp to a Jalali date string in the format:
/// 'weekday hour:minute:second year/month/day'. If no timestamp is provided, it returns an empty string.
///
/// # Arguments
/// * `timestamp` - Unix timestamp to be converted. If None, returns empty string.
///
/// # Returns
/// The converted Jalali date string or an empty string if no timestamp is provided.
///
/// Note: This function requires external Jalali date conversion libraries which are not
/// implemented in this basic conversion. Returns a placeholder for now.
pub fn convert_timestamp_to_jalali(timestamp: Option<i64>) -> String {
    match timestamp {
        Some(_ts) => {
            // TODO: Implement Jalali date conversion using appropriate Rust crates
            // like `persian-date` or similar
            "Jalali conversion not implemented".to_string()
        }
        None => String::new(),
    }
}

/// Convert a Gregorian datetime object to a Jalali datetime string.
///
/// This function takes a Gregorian datetime object and converts it to a Jalali (Persian) datetime string.
///
/// # Arguments
/// * `gregorian_object` - The Gregorian datetime object to convert. If None, returns empty string.
///
/// # Returns
/// The Jalali datetime string or an empty string if no datetime object is provided.
///
/// Note: This function requires external Jalali date conversion libraries which are not
/// implemented in this basic conversion. Returns a placeholder for now.
pub fn convert_to_jalali(gregorian_object: Option<DateTime<Utc>>) -> String {
    match gregorian_object {
        Some(_dt) => {
            // TODO: Implement Jalali date conversion using appropriate Rust crates
            "Jalali conversion not implemented".to_string()
        }
        None => String::new(),
    }
}

/// Convert a datetime object to seconds since the epoch.
///
/// # Arguments
/// * `date_obj` - A datetime object to be converted.
///
/// # Returns
/// The number of seconds since the epoch.
///
/// # Examples
/// ```
/// use chrono::{DateTime, Utc, TimeZone};
/// let date_obj = Utc.ymd(2023, 10, 26).and_hms(12, 0, 0);
/// assert_eq!(convert_to_second(date_obj), 1698321600);
/// ```
pub fn convert_to_second(date_obj: DateTime<Utc>) -> i64 {
    date_obj.timestamp()
}

/// Generate a unique ID for an HTMX indicator by joining the provided arguments with hyphens.
///
/// # Arguments
/// * `args` - Variable length argument list of strings to be joined.
///
/// # Returns
/// A string representing the unique ID for the HTMX indicator.
///
/// # Examples
/// ```
/// assert_eq!(create_id_for_htmx_indicator(&["by-date", "source-ip", "2024-06-30"]), "by-date-source-ip-2024-06-30--htmx-indicator");
/// assert_eq!(create_id_for_htmx_indicator(&["tops"]), "tops--htmx-indicator");
/// ```
pub fn create_id_for_htmx_indicator(args: &[&str]) -> String {
    let joined = args.join("-");
    let result = format!("{}--htmx-indicator", joined);

    // Replace 3 or more consecutive hyphens with double hyphens
    let regex = Regex::new(r"-{3,}").unwrap();
    regex.replace_all(&result, "--").to_string()
}

/// Generate a short UUID string.
///
/// This function creates a UUID (Universally Unique Identifier) and returns a shortened version of it.
///
/// # Returns
/// A shortened UUID string.
///
/// # Examples
/// ```
/// let short_uuid = create_short_uuid();
/// assert_eq!(short_uuid.len(), 8); // Should be 8 characters long
/// ```
pub fn create_short_uuid() -> String {
    let uuid = Uuid::new_v4();
    format!("{:x}", uuid.as_u128() & 0xffffffff)
}

/// Get the current date and time in Persian format.
///
/// This function retrieves the current date and time and formats it in Persian numerals.
///
/// # Returns
/// The current date and time in the format 'YYYY/MM/DD HH:MM' with Persian numerals.
///
/// Note: This function requires external Persian/Jalali date conversion libraries which are not
/// implemented in this basic conversion. Returns a placeholder for now.
pub fn get_date_time_live() -> String {
    // TODO: Implement Jalali date conversion and Persian numeral conversion
    // using appropriate Rust crates
    "Persian date/time conversion not implemented".to_string()
}

/// Get a list of files in a directory with a specific extension, sorted naturally.
///
/// # Arguments
/// * `directory` - The directory to search for files.
/// * `extension` - The file extension to filter by.
///
/// # Returns
/// A vector of absolute file paths with the specified extension, sorted naturally.
///
/// # Examples
/// ```
/// let files = get_list_of_files("/tmp", "txt");
/// // Returns vector of .txt files in /tmp directory
/// ```
pub fn get_list_of_files(directory: &str, extension: &str) -> Vec<String> {
    let path = Path::new(directory);

    if !path.exists() {
        return Vec::new();
    }

    let mut files = Vec::new();

    if let Ok(entries) = fs::read_dir(path) {
        for entry in entries.flatten() {
            let file_path = entry.path();

            if file_path.is_file() {
                if let Some(file_extension) = file_path.extension() {
                    if file_extension == extension {
                        if let Some(absolute_path) = file_path.to_str() {
                            files.push(absolute_path.to_string());
                        }
                    }
                }
            }
        }
    }

    // Sort naturally (this would require the natural_sort crate for full implementation)
    files.sort();
    files
}

/// Calculate the percentage of a smaller number relative to a total number.
///
/// # Arguments
/// * `smaller_number` - The part of the total number.
/// * `total_number` - The total number.
/// * `to_persian` - If true, returns the percentage in Persian numerals.
///
/// # Returns
/// The percentage as a string, optionally in Persian numerals.
///
/// # Examples
/// ```
/// assert_eq!(get_percent(25.0, 100.0, false), "25");
/// assert_eq!(get_percent(0.0, 100.0, false), "0");
/// assert_eq!(get_percent(25.0, 0.0, false), "0");
/// assert_eq!(get_percent(99.95232355216523, 100.0, false), "99.9");
/// ```
pub fn get_percent(smaller_number: f64, total_number: f64, to_persian: bool) -> String {
    if smaller_number == 0.0 || total_number == 0.0 {
        return if to_persian { "۰".to_string() } else { "0".to_string() };
    }

    let perc = (smaller_number * 100.0) / total_number;

    if perc as i32 == 0 {
        return if to_persian { "~۰".to_string() } else { "~0".to_string() };
    }

    // Round to 1 decimal place: 99.95232355216523 -> 99.9
    let rounded_perc = (perc * 10.0).round() / 10.0;

    // Remove trailing .0
    let perc_str = if rounded_perc.fract() == 0.0 {
        format!("{}", rounded_perc as i64)
    } else {
        format!("{:.1}", rounded_perc)
    };

    if to_persian {
        persianize(&perc_str)
    } else {
        perc_str
    }
}

/// Formats a Persian number string by adding commas as thousand separators.
///
/// This function supports both integer and floating-point Persian numbers.
/// For floating-point numbers, it correctly handles the decimal separator.
///
/// # Arguments
/// * `num` - The Persian number string to be formatted.
///
/// # Returns
/// The formatted Persian number string with commas as thousand separators.
///
/// # Examples
/// ```
/// assert_eq!(intcomma_persian("۱۲۳۴۵۶۷۸۹۰"), "۱،۲۳۴،۵۶۷،۸۹۰");
/// assert_eq!(intcomma_persian("۱۲۳۴۵۶۷۸۹۰.۱۲۳۴۵۶۷۸۹۰"), "۱،۲۳۴،۵۶۷،۸۹۰.۱۲۳۴۵۶۷۸۹۰");
/// ```
pub fn intcomma_persian(num: &str) -> String {
    let mut commad = String::new();
    let mut left = String::new();
    let mut right = String::new();
    let mut separator = String::new();
    let mut is_float = false;

    // Check if it's a float with decimal point
    let decimal_regex = Regex::new(r"^[۱۲۳۴۵۶۷۸۹۰]+\.[۱۲۳۴۵۶۷۸۹۰]+$").unwrap();
    if decimal_regex.is_match(num) {
        let parts: Vec<&str> = num.split('.').collect();
        left = parts[0].to_string();
        right = parts[1].to_string();
        separator = ".".to_string();
        is_float = true;
    }
    // Check if it's a float with slash separator
    else {
        let slash_regex = Regex::new(r"^[۱۲۳۴۵۶۷۸۹۰]+/[۱۲۳۴۵۶۷۸۹۰]+$").unwrap();
        if slash_regex.is_match(num) {
            let parts: Vec<&str> = num.split('/').collect();
            left = parts[0].to_string();
            right = parts[1].to_string();
            separator = "/".to_string();
            is_float = true;
        } else {
            left = num.to_string();
        }
    }

    // Add commas to the left part
    for (idx, char) in left.chars().rev().enumerate() {
        if idx % 3 == 0 && idx > 0 {
            commad = format!("{}،{}", char, commad);
        } else {
            commad = format!("{}{}", char, commad);
        }
    }

    if is_float {
        commad = format!("{}{}{}", commad, separator, right);
    }

    commad
}

/// Check if the given string represents an integer or a float.
///
/// # Arguments
/// * `string` - The string to be checked.
///
/// # Returns
/// `true` if the string represents an integer or a float, `false` otherwise.
///
/// # Examples
/// ```
/// assert_eq!(is_int_or_float("123"), true);
/// assert_eq!(is_int_or_float("123.456"), true);
/// assert_eq!(is_int_or_float("abc"), false);
/// assert_eq!(is_int_or_float("123abc"), false);
/// ```
pub fn is_int_or_float(string: &str) -> bool {
    INT_OR_FLOAT_PATTERN.is_match(string)
}

/// Convert an English number to its Persian equivalent.
///
/// This function takes a number string and converts it to a Persian string representation.
/// If the number is a float, it handles the decimal part appropriately.
///
/// # Arguments
/// * `number` - The number string to be converted.
///
/// # Returns
/// The Persian string representation of the number.
///
/// # Examples
/// ```
/// assert_eq!(persianize("123"), "۱۲۳");
/// assert_eq!(persianize("123.45"), "۱۲۳.۴۵");
/// assert_eq!(persianize("123.00"), "۱۲۳");
/// ```
pub fn persianize(number: &str) -> String {
    // Check if it's a float
    let float_regex = Regex::new(r"^[0-9]+\.[0-9]+$").unwrap();
    if float_regex.is_match(number) {
        let parts: Vec<&str> = number.split('.').collect();
        let left = parts[0];
        let right = parts[1];

        // Check if right part is all zeros
        let zeros_regex = Regex::new("^0+$").unwrap();
        if zeros_regex.is_match(right) {
            return english_to_persian(left);
        }

        // Take only first 2 decimal places
        let right_truncated = if right.len() > 2 { &right[..2] } else { right };
        return format!("{}.{}", english_to_persian(left), english_to_persian(right_truncated));
    }

    english_to_persian(number)
}

/// Helper function to convert English digits to Persian digits
fn english_to_persian(text: &str) -> String {
    text.chars()
        .map(|c| match c {
            '0' => '۰',
            '1' => '۱',
            '2' => '۲',
            '3' => '۳',
            '4' => '۴',
            '5' => '۵',
            '6' => '۶',
            '7' => '۷',
            '8' => '۸',
            '9' => '۹',
            _ => c,
        })
        .collect()
}

/// Sort a HashMap based on its keys or values.
///
/// # Arguments
/// * `dictionary` - The HashMap to be sorted.
/// * `based_on` - The criteria to sort by, either "key" or "value".
/// * `reverse` - If true, sort in descending order, otherwise ascending.
///
/// # Returns
/// A new HashMap sorted based on the specified criteria.
///
/// # Examples
/// ```
/// use std::collections::HashMap;
/// let mut dict = HashMap::new();
/// dict.insert("b".to_string(), 2);
/// dict.insert("a".to_string(), 1);
/// dict.insert("c".to_string(), 3);
/// let sorted = sort_dict(dict, "key", false);
/// // Returns HashMap sorted by keys in ascending order
/// ```
pub fn sort_dict(dictionary: HashMap<String, i32>, based_on: &str, reverse: bool) -> HashMap<String, i32> {
    let mut items: Vec<(String, i32)> = dictionary.into_iter().collect();

    match based_on {
        "key" => {
            if reverse {
                items.sort_by(|a, b| b.0.cmp(&a.0));
            } else {
                items.sort_by(|a, b| a.0.cmp(&b.0));
            }
        }
        "value" => {
            if reverse {
                items.sort_by(|a, b| b.1.cmp(&a.1));
            } else {
                items.sort_by(|a, b| a.1.cmp(&b.1));
            }
        }
        _ => {} // Return original order if invalid criteria
    }

    items.into_iter().collect()
}

/// Replaces the home directory path in the given text with a tilde (~).
///
/// # Arguments
/// * `text` - The text in which to replace the home directory path.
///
/// # Returns
/// The text with the home directory path replaced by a tilde.
///
/// # Examples
/// ```
/// assert_eq!(to_tilda("/home/<USER>/documents/file.txt"), "~/documents/file.txt");
/// assert_eq!(to_tilda("/home/<USER>/"), "~/");
/// ```
pub fn to_tilda(text: &str) -> String {
    if let Ok(home) = std::env::var("HOME") {
        text.replace(&home, "~")
    } else {
        text.to_string()
    }
}

// -----------------
// Note: Django-specific functions (admin actions, command utilities, etc.)
// have been omitted as they don't have direct Rust equivalents.
// These would need to be implemented using appropriate Rust web frameworks
// like Axum, Actix-web, or Rocket depending on your specific needs.
